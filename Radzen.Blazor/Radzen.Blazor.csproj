﻿<Project Sdk="Microsoft.NET.Sdk.Razor">
  <PropertyGroup>
    <IncludeSymbols>true</IncludeSymbols>
    <SymbolPackageFormat>snupkg</SymbolPackageFormat>
    <NoWarn>BL9993;BL0007;BL0005</NoWarn>
    <TargetFrameworks>net6.0;net7.0;net8.0;net9.0</TargetFrameworks>
    <RazorLangVersion>7.0</RazorLangVersion>
    <LangVersion>latest</LangVersion>
    <OutputType>Library</OutputType>
    <IsPackable>true</IsPackable>
    <PackageId>Radzen.Blazor</PackageId>
    <Product>Radzen.Blazor</Product>
    <Version>7.0.7</Version>
    <Copyright>Radzen Ltd.</Copyright>
    <Authors>Radzen Ltd.</Authors>
    <Description>Radzen Blazor is a set of 90+ free native Blazor UI controls packed with DataGrid, Scheduler, Charts and robust theming including Material design and Fluent UI.</Description>
    <PackageTags>blazor material design fluent fluentui components datagrid scheduler charts</PackageTags>
    <PackageReadmeFile>README.md</PackageReadmeFile>
    <PackageProjectUrl>https://www.radzen.com</PackageProjectUrl>
    <PackageIcon>icon.png</PackageIcon>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <Title>Radzen Components for Blazor</Title>
    <RepositoryUrl>https://github.com/radzenhq/radzen-blazor</RepositoryUrl>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DartSassBuilder" Version="1.1.0" PrivateAssets="All" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Condition="'$(TargetFramework)' == 'net6.0'" Version="6.0.25" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Condition="'$(TargetFramework)' == 'net6.0'" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Condition="'$(TargetFramework)' == 'net7.0'" Version="7.0.14" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Condition="'$(TargetFramework)' == 'net7.0'" Version="7.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Condition="'$(TargetFramework)' == 'net8.0'" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Condition="'$(TargetFramework)' == 'net8.0'" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components" Condition="'$(TargetFramework)' == 'net9.0'" Version="9.*-*" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Condition="'$(TargetFramework)' == 'net9.0'" Version="9.*-*" />
    <PackageReference Include="Radzen.Terser.MSBuild" Version="0.0.4" PrivateAssets="All" />
  </ItemGroup>

  <ItemGroup>
    <None Include="LICENSE.txt" Pack="true" PackagePath="" />
    <None Include="icon.png" Pack="true" PackagePath="" />
    <None Include="..\README.md" Pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="LinkerConfig.xml">
      <LogicalName>$(MSBuildProjectName).xml</LogicalName>
    </EmbeddedResource>
  </ItemGroup>

  <PropertyGroup>
    <DartSassOutputStyle>expanded</DartSassOutputStyle>
    <EnableDefaultSassItems>false</EnableDefaultSassItems>
  </PropertyGroup>

  <ItemGroup>
    <Sass Include="$(MSBuildProjectDirectory)/themes/*.scss" Exclude="$(MSBuildProjectDirectory)/themes/_*.scss" Condition="'$(TargetFramework)' == 'net9.0'" />
  </ItemGroup>

 <Target Name="Sass" BeforeTargets="BeforeBuild" Condition="'$(TargetFramework)' == 'net9.0'">
    <PropertyGroup>
      <_SassFileList>@(Sass->'&quot;%(FullPath)&quot;', ' ')</_SassFileList>
      <DartSassBuilderArgs>files $(_SassFileList) --outputstyle $(DartSassOutputStyle) --level $(DartSassOutputLevel)</DartSassBuilderArgs>
    </PropertyGroup>
    <Message Text="$(DartSassBuilderArgs)" Importance="$(DartSassMessageLevel)" />
    <Message Text="Converted SassFile list to argument" Importance="$(DartSassMessageLevel)" />
  </Target>

  <Target Name="MoveCss" AfterTargets="AfterCompile" Condition="'$(TargetFramework)' == 'net9.0'">
    <ItemGroup>
        <CssFile Include="$(MSBuildProjectDirectory)/themes/*.css" />
    </ItemGroup>
    <Move SourceFiles="@(CssFile)" DestinationFolder="$(MSBuildProjectDirectory)/wwwroot/css/" />
  </Target>

  <Target Name="MinifyTest" BeforeTargets="Build" Condition="'$(TargetFramework)' == 'net9.0'">
    <TerserMinify InputFile="wwwroot\Radzen.Blazor.js" OutputFile="wwwroot\Radzen.Blazor.min.js" />
  </Target>

  <ItemGroup Label="Allow internal method to be visible to the unit tests">
    <AssemblyAttribute Include="System.Runtime.CompilerServices.InternalsVisibleTo">
      <_Parameter1>Radzen.Blazor.Tests</_Parameter1>
    </AssemblyAttribute>
  </ItemGroup>

</Project>
