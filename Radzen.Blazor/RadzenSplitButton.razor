﻿@using Microsoft.JSInterop

@inherits RadzenComponentWithChildren
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()"
         tabindex="@(Disabled ? -1 : TabIndex)" @onkeydown="@OnKeyPress" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
        <button aria-label="@(ButtonAriaLabel ?? Text)" tabindex="-1" disabled="@IsDisabled" class=@ButtonClass type="@Enum.GetName(typeof(ButtonType), ButtonType).ToLower()" @onclick="@OnClick">
            <span class="rz-button-box">
            @if (ButtonContent != null)
            {
                @ButtonContent
            }
            else
            {
                @if (IsBusy)
                {
                    <RadzenIcon Icon="refresh" Style="animation: rotation 700ms linear infinite" />
                    @if (!string.IsNullOrEmpty(BusyText))
                    {
                        <span class="rz-button-text">@BusyText</span>
                    }
                }
                else
                {
                    @if (!string.IsNullOrEmpty(@Icon))
                    {
                        <i class="notranslate rz-button-icon-left rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
                    }
                    @if (!string.IsNullOrEmpty(Image))
                    {
                        <img class="notranslate rz-button-icon-left rzi" src="@Image" alt=@ImageAlternateText />
                    }
                    @if (!string.IsNullOrEmpty(Text))
                    {
                        <span class="rz-button-text">@Text</span>
                    }
                    else
                    {
                        <span class="rz-button-text">&nbsp;</span>
                    }
                }
            }
            </span>
        </button>
        <button tabindex="-1" disabled="@IsDisabled" onclick="@OpenPopupScript()" class=@PopupButtonClass type="button" aria-label="@OpenAriaLabel">
            <RadzenIcon Icon="@DropDownIcon" />
        </button>
        <div id="@PopupID" class="rz-splitbutton-menu">
            <ul class="rz-menu-list">
                <CascadingValue Value=this>
                    @ChildContent
                </CascadingValue>
            </ul>
        </div>
    </div>
}
