@using Radzen.Blazor
@if (Chart.Legend.Visible)
{
    <div class="@Class">
        <div class="rz-legend-items">
        @foreach (var series in Chart.Series)
        {
            @if (series.ShowInLegend)
            {
                @series.RenderLegendItem()
            }
        }
        </div>
    </div>
}
@code {
    [CascadingParameter]
    public RadzenChart Chart { get; set; }

    string Class => ClassList.Create("rz-legend")
        .Add($"rz-legend-{Chart.Legend.Position}".ToLowerInvariant())
        .ToString();
}
