@typeparam TItem

@foreach(var point in Data)
{
    var x = point.X;
    var y = point.Y;

    @if (Visible && InPlotArea(x, y))
    {
        <Marker Click="@(() => OnClick(point.Data))" X="@x" Y="@y" Type="@MarkerType" Fill="@Fill" Stroke="@Stroke" StrokeWidth="@StrokeWidth" Size="@Size" />
    }
}

@code {
    [CascadingParameter]
    public RadzenChart Chart { get; set;}

    [Parameter]
    public IChartSeries Series { get; set; }

    private async Task OnClick(TItem data)
    {
        await Series.InvokeClick(Chart.SeriesClick, data);
    }

    [Parameter]
    public IEnumerable<Point<TItem>> Data { get; set; }

    [Parameter]
    public bool Visible { get; set; }

    [Parameter]
    public MarkerType MarkerType { get; set; }

    [Parameter]
    public string Stroke { get; set; }

    [Parameter]
    public string Fill { get; set; }

    [Parameter]
    public double StrokeWidth { get; set; }

    [Parameter]
    public double Size { get; set; }

    private double x1;
    private double x2;

    private double y1;
    private double y2;

    private bool InPlotArea(double x, double y)
    {
        return x >= x1 && x <= x2 && y >= y1 && y <= y2;
    }

    protected override void OnParametersSet()
    {
        var categoryTicks = Chart.CategoryScale.Ticks(Chart.CategoryAxis.TickDistance);

        x1 = Chart.CategoryScale.Scale(categoryTicks.Start);
        x2 = Chart.CategoryScale.Scale(categoryTicks.End);

        var valueTicks = Chart.ValueScale.Ticks(Chart.ValueAxis.TickDistance);

        y1 = Chart.ValueScale.Scale(valueTicks.End);
        y2 = Chart.ValueScale.Scale(valueTicks.Start);
    }
}
