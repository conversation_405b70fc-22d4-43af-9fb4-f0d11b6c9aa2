@using Radzen.Blazor

<g class="rz-marker">
@switch(Type)
{
    case MarkerType.Circle:
        <circle cx="@X.ToInvariantString()" cy="@Y.ToInvariantString()" r="@Size.ToInvariantString()" fill="@Fill" stroke="@Stroke" stroke-width="@StrokeWidth.ToInvariantString()" @onclick="@Click"></circle>
    break;

    case MarkerType.Square:
        <path d="@($"M {(X-Size).ToInvariantString()} {(Y-Size).ToInvariantString()} L {(X+Size).ToInvariantString()} {(Y-Size).ToInvariantString()} L {(X+Size).ToInvariantString()} {(Y+Size).ToInvariantString()} L {(X-Size).ToInvariantString()} {(Y + Size).ToInvariantString()} Z")" stroke="@Stroke" fill="@Fill" stroke-width="@StrokeWidth.ToInvariantString()" @onclick="@Click"></path>
    break;

    case MarkerType.Triangle:
        <path d="@($"M {(X-Size).ToInvariantString()} {(Y+Size).ToInvariantString()} L {X.ToInvariantString()} {(Y-Size).ToInvariantString()} L {(X+Size).ToInvariantString()} {(Y+Size).ToInvariantString()} Z")" stroke="@Stroke" fill="@Fill" stroke-width="@StrokeWidth.ToInvariantString()" @onclick="@Click"></path>
    break;

    case MarkerType.Diamond:
        <path d="@($"M {(X-Size).ToInvariantString()} {Y.ToInvariantString()} L {X.ToInvariantString()} {(Y-Size).ToInvariantString()} L {(X+Size).ToInvariantString()} {Y.ToInvariantString()} L {X.ToInvariantString()} {(Y + Size).ToInvariantString()} Z")" stroke="@Stroke" fill="@Fill" stroke-width="@StrokeWidth.ToInvariantString()" @onclick="@Click"></path>
    break;
}
</g>

@code {
    [Parameter]
    public double X { get; set; }

    [Parameter]
    public MarkerType Type { get; set; }

    [Parameter]
    public double Y { get; set; }

    [Parameter]
    public string Stroke { get; set; }

    [Parameter]
    public double StrokeWidth { get; set; }

    [Parameter]
    public string Fill { get; set; }

    [Parameter]
    public double Size { get; set; }

    [Parameter]
    public EventCallback Click { get; set; }
}
