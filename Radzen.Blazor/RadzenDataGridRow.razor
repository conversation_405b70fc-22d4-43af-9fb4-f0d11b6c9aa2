@using Microsoft.AspNetCore.Components.Forms
@typeparam TItem
@implements IRadzenForm
 <CascadingValue Value=@EditContext>
    <CascadingValue Value=this>
@{var rowArgs = Grid?.RowAttributes(Item, Index); }
@{var firstLevel = Grid.AllowCompositeDataCells ? 0 : Grid.deepestChildColumnLevel; }
@for(var i = firstLevel; Grid.AllowCompositeDataCells ? i <= Grid.deepestChildColumnLevel + 1 : i < Grid.deepestChildColumnLevel  + 1; i++)
{
<tr class="@(Grid.RowStyle(Item, Index))" @attributes="@rowArgs.Item2">
    @if (Grid.ShowGroupExpandColumn)
    {
        @foreach(var g in Grid.Groups)
        {
            @if (i == firstLevel)
            {
              <td class="rz-col-icon" rowspan="@(Grid.AllowCompositeDataCells ? (Grid.deepestChildColumnLevel + 1) : 1)">
                  <span class="rz-column-title"></span>
              </td>
            }
           
        }
    }
    @if (Grid.Template != null && Grid.ShowExpandColumn && i == firstLevel)
    {
        <td class="rz-col-icon" rowspan="@(Grid.AllowCompositeDataCells ? (Grid.deepestChildColumnLevel + 1) : 1)">
            <span class="rz-column-title"></span>
            @if (rowArgs.Item1.Expandable && !Grid.LoadChildData.HasDelegate)
            {
                <a id="@(Grid.GridId() + Item.GetHashCode())" aria-label="@Grid.ExpandChildItemAriaLabel" @onclick:preventDefault="true" @onclick="@(_ => Grid.ExpandItem(Item))" @onclick:stopPropagation>
                    <span class="@(Grid.ExpandedItemStyle(Item))"></span>
                </a>
            }
        </td>
    }

        @for (var j = 0; j < Columns.Count; j++)
        {
            if (Grid.rowSpans.ContainsKey(j))
            {
                Grid.rowSpans[j] = Grid.rowSpans[j] - 1;

                if (Grid.rowSpans[j] <= 0)
                {
                    Grid.rowSpans.Remove(j);
                }
                else
                {
                    continue;
                }
            }

            var column = Columns[j];
            var cellAttr = Grid.CellAttributes(Item, column);

            object colspan;
            cellAttr.TryGetValue("colspan", out colspan);

            if (colspan != null)
            {
                j = j + (int)Convert.ChangeType(colspan, TypeCode.Int32) - 1;
            }

            object rowspan;
            cellAttr.TryGetValue("rowspan", out rowspan);

            if (rowspan != null)
            {
                Grid.rowSpans.Add(j, (int)Convert.ChangeType(rowspan, TypeCode.Int32));
            }

            @Grid.RenderCell(column, Item, cellAttr, rowArgs, i);
        }

</tr>
}

@if (Grid.expandedItems.Keys.Contains(Item))
{
    @if (Grid.IsRowInEditMode(Item) && Grid.EditTemplate != null)
    {
        <tr class="rz-expanded-row-content" @onkeydown:stopPropagation>
            <td colspan="@(Columns.Sum(c => c.GetColSpan()) + (Grid.ShowExpandColumn ? 1 : 0) + Grid.Groups.Count)">
                <div class="rz-expanded-row-template" style="position:sticky">
                    <CascadingValue Value=@EditContext>
                        <CascadingValue Value=this>
                            @Grid.EditTemplate(Item)
                        </CascadingValue>
                    </CascadingValue>
                </div>
            </td>
        </tr>
    }
    else if (Grid.Template != null)
    {
        <tr class="rz-expanded-row-content" @onkeydown:stopPropagation>
            <td colspan="@(Columns.Sum(c => c.GetColSpan()) + (Grid.ShowExpandColumn ? 1 : 0) + Grid.Groups.Count)">
                <div class="rz-expanded-row-template" style="position:sticky">
                    @Grid.Template(Item)
                </div>
            </td>
        </tr>
    }
}
    </CascadingValue>
</CascadingValue>
@code {
        [Parameter]
        public IList<RadzenDataGridColumn<TItem>> Columns { get; set; }

        [Parameter]
        public TItem Item { get; set; }

        [Parameter]
        public int Index { get; set; }

        [Parameter]
        public RadzenDataGrid<TItem> Grid { get; set; }

        [Parameter(CaptureUnmatchedValues = true)]
        public IReadOnlyDictionary<string, object> Attributes { get; set; }

        [Parameter]
        public RenderFragment ChildContent { get; set; }

        [Parameter]
        public string CssClass { get; set; }

        [Parameter]
        public bool InEditMode { get; set; }

        [Parameter]
        public EditContext EditContext { get; set; }

        List<IRadzenFormComponent> components;

        public void AddComponent(IRadzenFormComponent component)
        {
            if (components != null)
            {
                if (components.IndexOf(component) == -1)
                {
                    components.Add(component);
                }
            }
        }
        public void RemoveComponent(IRadzenFormComponent component)
        {
            components?.Remove(component);
        }

        public override Task SetParametersAsync(ParameterView parameters)
        {
            if (InEditMode != parameters.GetValueOrDefault<bool>("InEditMode"))
            {
                components = new List<IRadzenFormComponent>();
            }

            return base.SetParametersAsync(parameters);
        }

        public IRadzenFormComponent FindComponent(string name)
        {
            return components.Where(component => component.Name == name).FirstOrDefault();
        }
}