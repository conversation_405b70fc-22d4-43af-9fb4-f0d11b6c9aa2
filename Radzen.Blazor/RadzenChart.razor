@using Microsoft.JSInterop
@using Radzen.Blazor
@using Radzen.Blazor.Rendering

@inherits RadzenComponent
@if (Visible)
{
<CascadingValue Value="@this">
@ChildContent
</CascadingValue>
<div @ref="Element" @attributes="@Attributes" class="@GetCssClass()" style="@Style" id="@GetId()">
@if (Width.HasValue && Height.HasValue)
{
    <CascadingValue Value="@this">
        <Legend />
        <svg style="width: 100%; height: 100%; overflow: visible;">
            <g transform="@($"translate({MarginLeft.ToInvariantString()}, {MarginTop.ToInvariantString()})")">
                <ClipPath />
                @if(ShouldRenderAxes())
                {
                    <ValueAxis />
                    <CategoryAxis />
                }
                @foreach (var series in Series.OrderBy(s => s.RenderingOrder))
                {
                    @if (series.Visible)
                    {
                        @series.Render(CategoryScale, ValueScale);
                        @series.RenderOverlays(CategoryScale, ValueScale);
                    }
                }
            </g>
        </svg>
        @foreach (var series in Series.OrderBy(s => s.RenderingOrder))
        {
            @if (series.Visible && series is IChartDonutSeries donut)
            {
                @donut.RenderTitle(MarginLeft, MarginTop)
            }
        }

    </CascadingValue>
}
</div>
}
