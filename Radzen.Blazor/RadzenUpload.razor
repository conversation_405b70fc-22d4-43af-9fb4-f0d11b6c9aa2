﻿@using <PERSON><PERSON><PERSON>
@using Microsoft.JSInterop
@inherits RadzenComponent
<CascadingValue Value=@this>
    @ChildContent
</CascadingValue>
@if (Visible)
{
<div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()">
    <div class="rz-fileupload-buttonbar">
        <span class="@ChooseClassList" tabindex="@(Disabled ? -1 : 0)" onkeydown="if(event.keyCode == 32 || event.keyCode == 13){event.preventDefault();this.firstElementChild.click();}">
            @RenderInput()
            @if (!string.IsNullOrEmpty(Icon))
            {
            <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
            }
            <span class="rz-button-text">@ChooseText</span>
        </span>
    </div>
    @if (files.Any())
    {
    <div class="rz-fileupload-content">
        <div class="rz-fileupload-files">
            <div>
                @foreach (var file in files)
                {
                <div class="rz-fileupload-row" style="margin-bottom: 10px">
                    @if (!string.IsNullOrEmpty(file.Url))
                    {
                    <div><img src="@file.Url" width="50px" onerror="this.style.display='none';" alt="@ImageAlternateText"></div>
                    }
                    <div>@file.Name</div>
                    <div>@(file.Size / 1024) KB</div>
                    <div>
                        <button title=@DeleteText tabindex="0" disabled="@Disabled" @onclick="@(args => OnRemove(file))" type="button" class="@ButtonClassList"><span class="rz-icon-trash" style="display:block"></span></button>
                    </div>
                </div>
                }
            </div>
        </div>
    </div>
    }
</div>
}
@code {
    RenderFragment RenderInput()
    {
        return __builder => {
            <text>
                @if(!string.IsNullOrEmpty(Url))
                {
                    <input id="@(Name ?? GetId())" @ref="@fileUpload" @attributes="InputAttributes" type="file" multiple="@Multiple" accept="@Accept" disabled="@Disabled" 
                        onchange="Radzen.uploadInputChange(event, '@Url', @Auto.ToString().ToLower(), @Multiple.ToString().ToLower(), true, '@ParameterName')" tabindex="-1" onkeydown="event.stopPropagation()"/>
                }
                else
                {
                    <Microsoft.AspNetCore.Components.Forms.InputFile id="@(Name ?? GetId())" @attributes="InputAttributes" multiple="@Multiple" accept="@Accept" disabled="@Disabled" 
                        tabindex="-1" onkeydown="event.stopPropagation()" OnChange="@OnInputChange" />
                }
            </text>
            };
    }
 }