﻿@inherits RadzenComponent
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor.Rendering;
@if (Visible)
{
    <div @ref="@Element" style="@($"--rz-progressbar-value: {(NormalizedValue * 100).ToInvariantString()}%;{Style}")" aria-valuemax="@Max" aria-valuemin="@Min" role="progressbar" @attributes="Attributes" class="@GetCssClass()"
         aria-valuenow="@Value" id="@GetId()">
        <div class="rz-progressbar-value"></div>
        @if (ShowValue)
        {
            <div class="rz-progressbar-label">
                @if(Template != null)
                {
                    @Template
                }
                else
                {
                    @Value@Unit
                    <span aria-hidden="true" class="rz-progressbar-label-value">@Value@Unit</span>
                }
            </div>
        }
    </div>
}
