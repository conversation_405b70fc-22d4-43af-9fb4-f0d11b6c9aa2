﻿@using System.Linq.Expressions
@typeparam TItem
@inherits PagedDataBoundComponent<TItem>
@if (Visible)
{
    <div @ref="@Element" @attributes="Attributes" class="@GetCssClass()" style="@Style" id="@GetId()">
        @if (AllowPaging && PagerPosition.HasFlag(PagerPosition.Top))
        {
                    <RadzenPager HorizontalAlign="@PagerHorizontalAlign" AlwaysVisible="@PagerAlwaysVisible" @ref="topPager" Count="@Count" PageSize="@PageSize" PageNumbersCount="@PageNumbersCount" PageChanged="@OnPageChanged" PageSizeChanged="@OnPageSizeChanged" PageSizeOptions="@PageSizeOptions" ShowPagingSummary="@ShowPagingSummary" PagingSummaryFormat="@PagingSummaryFormat" PagingSummaryTemplate="@PagingSummaryTemplate" PageSizeText="@PageSizeText" Density="@Density" FirstPageTitle="@FirstPageTitle" FirstPageAriaLabel="@FirstPageAriaLabel" PrevPageAriaLabel="@PrevPageAriaLabel" PrevPageTitle="@PrevPageTitle" NextPageAriaLabel="@NextPageAriaLabel" NextPageTitle="@NextPageTitle" LastPageAriaLabel="@LastPageAriaLabel" LastPageTitle="@LastPageTitle" PageAriaLabelFormat="@PageAriaLabelFormat" PageTitleFormat="@PageTitleFormat" PrevPageLabel="@PrevPageLabel" NextPageLabel="@NextPageLabel" />
        }
        @if (Data != null)
        {
            @if (!ShowEmptyMessage || Count > 0 && (AllowVirtualization ? Count > 0 : true) || LoadData.HasDelegate && Data.Count() > 0)
            {
                @if (!WrapItems)
                {
                    @DrawDataListRows()
                }
                else
                {
                <div class="rz-g">
                    @DrawDataListRows()
                </div>
                }
            }
            else
            {
                @if (EmptyTemplate != null)
                {
                    @EmptyTemplate
                }
                else
                {
                    <span style="white-space: normal">@EmptyText</span>
                }
            }
        }
        @if (IsLoading)
        {
            <div class="rz-datatable-loading"></div>
            <div class="rz-datatable-loading-content">
                @if(LoadingTemplate != null)
                {
                @LoadingTemplate
                }
                else
                {
                <i class="notranslate rzi-circle-o-notch"></i>
                }
            </div>
        }
        @if (AllowPaging && PagerPosition.HasFlag(PagerPosition.Bottom))
        {
                    <RadzenPager HorizontalAlign="@PagerHorizontalAlign" AlwaysVisible="@PagerAlwaysVisible" @ref="bottomPager" Count="@Count" PageSize="@PageSize" PageNumbersCount="@PageNumbersCount" PageChanged="@OnPageChanged" PageSizeChanged="@OnPageSizeChanged" PageSizeOptions="@PageSizeOptions" ShowPagingSummary="@ShowPagingSummary" PagingSummaryFormat="@PagingSummaryFormat" PagingSummaryTemplate="@PagingSummaryTemplate" PageSizeText="@PageSizeText" class="rz-pager-bottom" Density="@Density" FirstPageTitle="@FirstPageTitle" FirstPageAriaLabel="@FirstPageAriaLabel" PrevPageAriaLabel="@PrevPageAriaLabel" PrevPageTitle="@PrevPageTitle" NextPageAriaLabel="@NextPageAriaLabel" NextPageTitle="@NextPageTitle" LastPageAriaLabel="@LastPageAriaLabel" LastPageTitle="@LastPageTitle" PageAriaLabelFormat="@PageAriaLabelFormat" PageTitleFormat="@PageTitleFormat" PrevPageLabel="@PrevPageLabel" NextPageLabel="@NextPageLabel" />
        }
    </div>
}
