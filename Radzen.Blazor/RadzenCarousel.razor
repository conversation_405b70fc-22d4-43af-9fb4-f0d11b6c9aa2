﻿@inherits RadzenComponent
@using System.Linq
@using Microsoft.JSInterop
<CascadingValue Value=this>
@if (Visible)
{
    <section @ref=@Element style=@Style @attributes=@Attributes class=@GetCssClass() id=@GetId() tabindex="0">
        @if(AllowPaging && (PagerPosition == PagerPosition.Top || PagerPosition == PagerPosition.TopAndBottom))
        {
        <RadzenStack class="rz-carousel-pager rz-carousel-pager-top" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Wrap="FlexWrap.Wrap">
            @foreach (var item in items)
            {
                var index = items.IndexOf(item);
                <a @onclick="@(args => Navigate(index))" class="rz-carousel-pager-button @(index == selectedIndex ? "rz-state-active" : "")"></a>
            }
        </RadzenStack>
        }
        @if(AllowNavigation)
        {
        <RadzenButton class="rz-carousel-prev" Icon="@PrevIcon" Text="@PrevText" ButtonStyle="@ButtonStyle" Size="@ButtonSize" Variant="@ButtonVariant" Shade="@ButtonShade"
            Click=@Prev />
        <RadzenButton class="rz-carousel-next" Icon="@NextIcon" Text="@NextText" ButtonStyle="@ButtonStyle" Size="@ButtonSize" Variant="@ButtonVariant" Shade="@ButtonShade"
            Click=@Next/>
        }
        <ul class="rz-carousel-items" 
                 @ontouchstart="OnTouchStart"
                 @ontouchend="OnTouchEnd" 
                 @ontouchcancel="OnTouchCancel">
            @Items
        </ul>
        @if(AllowPaging && (PagerPosition == PagerPosition.Bottom || PagerPosition == PagerPosition.TopAndBottom))
        {
        <RadzenStack class="rz-carousel-pager rz-carousel-pager-bottom" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Wrap="FlexWrap.Wrap">
            @foreach (var item in items)
            {
                var index = items.IndexOf(item);
                <a @onclick="@(args => Navigate(index))" class="rz-carousel-pager-button @(index == selectedIndex ? "rz-state-active" : "")"></a>
            }
        </RadzenStack>
        }
    </section>
}
</CascadingValue>
