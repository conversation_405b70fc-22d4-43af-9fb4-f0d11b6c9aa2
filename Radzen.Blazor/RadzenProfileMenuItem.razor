﻿@using Microsoft.AspNetCore.Components.Routing
@inherits RadzenComponent
@if (Visible)
{
    <li @attributes="Attributes" class="@GetItemCssClass()" style="@Style" @onclick="@OnClick">
        <div class="rz-navigation-item-wrapper">
            @if (Path != null)
            {
                <NavLink tabindex="-1" target="@Target" class="rz-navigation-item-link" href="@Path" Match="@Match">
                    @if (!string.IsNullOrEmpty(Icon))
                    {
                        <i class="notranslate rzi rz-navigation-item-icon" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
                    }
                    @if (!string.IsNullOrEmpty(Image))
                    {
                        <img class="notranslate rzi rz-navigation-item-icon" src="@Image" alt=@ImageAlternateText />
                    }
                    <span class="rz-navigation-item-text">@Text</span>
                </NavLink>
            }
            else
            {
                <div class="rz-navigation-item-link" >
                    @if (!string.IsNullOrEmpty(Icon))
                    {
                        <i class="notranslate rzi rz-navigation-item-icon">@Icon</i>
                    }
                    @if (!string.IsNullOrEmpty(Image))
                    {
                        <img class="notranslate rzi rz-navigation-item-icon" src="@Image" alt=@ImageAlternateText />
                    }
                    <span class="rz-navigation-item-text">@Text</span>
                </div>
            }
        </div>
    </li>
}
