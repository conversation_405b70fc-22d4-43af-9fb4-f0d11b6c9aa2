﻿@using <PERSON><PERSON><PERSON>
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Rendering
@typeparam TValue
@inherits DropDownBase<TValue>
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" tabindex="@(Disabled ? "-1" : $"{TabIndex}")" @onkeydown="@OnKeyDown" id="@GetId()">
        <div class="rz-helper-hidden-accessible">
            <input name="@Name" readonly="@ReadOnly" disabled="@Disabled" type="text" aria-label="@(!Multiple && internalValue != null ? internalValue : EmptyAriaLabel)" @attributes=@InputAttributes />
        </div>
        @if (View != null && (AllowFiltering || (Multiple && AllowSelectAll) || HeaderTemplate != null))
        {
            @if (HeaderTemplate != null)
            {
                @HeaderTemplate
            }
            else
            {
            <div class=" rz-listbox-header rz-helper-clearfix rz-listbox-header-w-checkbox">
                @if (Multiple)
                {
                    @if (AllowSelectAll)
                    {
                    <div class="rz-chkbox " @onclick="@SelectAll">
                        <div class="rz-helper-hidden-accessible">
                            <input id="@($"{UniqueID}sa")" readonly="readonly" type="checkbox" aria-label="@SelectAllText" aria-checked="@(IsAllSelected().ToString().ToLowerInvariant())">
                        </div>
                        <div class="@(IsAllSelected() ? "notranslate rz-chkbox-box rz-state-active" : "notranslate rz-chkbox-box")">
                            <span class="@(IsAllSelected() ? "notranslate rz-chkbox-icon rzi rzi-check" : "notranslate rz-chkbox-icon")"></span>
                        </div>
                    </div>
                    }
                    @if (AllowSelectAll && !AllowFiltering && !string.IsNullOrEmpty(Placeholder))
                    {
                        <label for="@($"{UniqueID}sa")" class="rz-dropdown-label rz-inputtext" style="width:100%;">@Placeholder</label>
                    }
                }
                @if (AllowFiltering)
                {
                    <div class="rz-listbox-filter-container">
                        @RenderFilter()
                        <span class="notranslate rz-listbox-filter-icon rzi rzi-search"></span>
                    </div>
                }
            </div>
            }
        }
        @if (View != null)
        {
            <div class="rz-listbox-list-wrapper">
                <ul @ref="list" class="rz-listbox-list">
                    @RenderItems()
                </ul>
            </div>
        }
    </div>
}
@code {
    internal RenderFragment RenderFilter()
    {
#if NET7_0_OR_GREATER
        return __builder => {
            <text>
                <input id="@SearchID" @ref="@search" disabled="@Disabled" class="rz-inputtext" role="textbox" type="text" placeholder="@Placeholder"
                               @onchange="@OnFilter" @onkeydown="@OnFilterKeyPress" @onkeydown:stopPropagation="true"
                               @bind:event="oninput" @bind:get="searchText" @bind:set=@(args => { searchText = $"{args}"; SearchTextChanged.InvokeAsync(searchText);}) aria-label="@SearchAriaLabel" />
            </text>
        };
#else
        return __builder => {
            <text>
                <input id="@SearchID" @ref="@search" disabled="@Disabled" class="rz-inputtext" role="textbox" type="text" placeholder="@Placeholder"
                               @onchange="@OnFilter" @onkeydown="@OnFilterKeyPress" value="@searchText" @onkeydown:stopPropagation="true"
                               @oninput=@((ChangeEventArgs args) => { searchText = $"{args.Value}"; SearchTextChanged.InvokeAsync(searchText);}) aria-label="@SearchAriaLabel" />
            </text>
        };
#endif
    }
}
