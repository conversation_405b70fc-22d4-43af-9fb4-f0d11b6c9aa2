$listbox-background-color: var(--rz-input-background-color) !default;
$listbox-border-radius: var(--rz-input-border-radius) !default;
$listbox-border: var(--rz-input-border) !default;
$listbox-focus-border: var(--rz-input-focus-border) !default;
$listbox-focus-shadow: var(--rz-input-focus-shadow) !default;
$listbox-filter-border: var(--rz-border-base-100) !default;
$listbox-padding: 0 !default;
$listbox-item-padding: var(--rz-dropdown-item-padding) !default;
$listbox-item-margin: 0 !default;
$listbox-checkbox-margin-block: 0 !default;
$listbox-checkbox-margin-inline: 0 0.5rem !default;
$listbox-header-padding-block: 0.5rem !default;
$listbox-header-padding-inline: 0.5rem !default;
$listbox-header-icon-width: var(--rz-icon-size) !default;
$listbox-header-icon-height: $listbox-header-icon-width !default;
$listbox-header-icon-margin: 0 !default;

.rz-listbox {
  box-sizing: border-box;
  display: inline-flex;
  flex-direction: column;
  overflow: hidden;
  background-color: var(--rz-listbox-background-color);
  border: var(--rz-listbox-border);
  border-radius: var(--rz-listbox-border-radius);
  text-align: start;

  .rz-chkbox {
    margin-block: var(--rz-listbox-checkbox-margin-block);
    margin-inline: var(--rz-listbox-checkbox-margin-inline);
  }

  &.rz-state-disabled {
    @extend %input-disabled;
  }

  &:not(.rz-state-disabled) {
    .rz-listbox-item {
      &:hover {
        @include dropdown-item-hover();
      }
    }
  }

  &:focus,
  &:focus-within {
    outline: var(--rz-outline-normal);
    border: var(--rz-listbox-focus-border);
    box-shadow: var(--rz-listbox-focus-shadow);
  }
}

.rz-listbox-list {
  margin: 0;
  padding: 0;
}

.rz-listbox-item {
  cursor: default;
  padding: var(--rz-listbox-item-padding);
  margin: var(--rz-listbox-item-margin);

  @include dropdown-item();
}

.rz-listbox-header {
  display: flex;
  align-items: center;
  padding-block: var(--rz-listbox-header-padding-block);
  padding-inline: var(--rz-listbox-header-padding-inline);
  border-bottom: var(--rz-listbox-filter-border);
}

.rz-listbox-list-wrapper {
  flex: auto;
  overflow: auto;
  padding: var(--rz-listbox-padding);
}

%filter-container {
  display: flex;
  flex-direction: row-reverse;
  align-items: center;

  .rz-inputtext {
    flex: auto;
    width: 0;
    border: none;
    line-height: var(--rz-input-line-height);

    &:focus {
      outline: none;
    }
  }

  .rzi-search {
    width: var(--rz-listbox-header-icon-width);
    height: var(--rz-listbox-header-icon-height);
    line-height: var(--rz-listbox-header-icon-height);
    font-size: var(--rz-listbox-header-icon-height);

    &:before {
      content: 'search';
    }
  }
}

.rz-listbox-filter-container {
  @extend %filter-container;
  flex: auto;

  .rz-inputtext {
    background-color: transparent;
  }
}
