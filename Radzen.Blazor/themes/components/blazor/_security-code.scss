$rz-security-code-input-min-width: var(--rz-input-height) !default;
$rz-security-code-input-min-height: var(--rz-input-height) !default;
$rz-security-code-input-padding: 0.125rem !default;
$rz-security-code-input-font-size: 1.25rem !default;
$rz-security-code-input-font-weight: 600 !default;
$rz-security-code-input-line-height: 1 !default;

.rz-security-code {
  box-sizing: border-box;
  display: inline-flex;
  border-radius: var(--rz-input-border-radius);
}

.rz-security-code-wrapper {
  flex: 1;
  --rz-gap: 0.5rem;
}

.rz-security-code-input {
  text-align: center;
  min-width: var(--rz-security-code-input-min-width);
  min-height: var(--rz-security-code-input-min-height);
  width: 100%;
  height: 100%;
  padding: var(--rz-security-code-input-padding);
  font-size: var(--rz-security-code-input-font-size);
  font-weight: var(--rz-security-code-input-font-weight);
  line-height: var(--rz-security-code-input-line-height);
}
