$chart-axis-color: var(--rz-base-300) !default;
$chart-axis-label-color: var(--rz-text-secondary-color) !default;
$chart-axis-font-size: 0.875rem !default;
$chart-legend-font-size: 0.875rem !default;
$chart-legend-focus-outline: var(--rz-outline-focus) !default;
$chart-legend-focus-outline-offset: var(--rz-outline-offset) !default;
$chart-tooltip-background: var(--rz-base-background-color) !default;
$chart-tooltip-color: var(--rz-text-color) !default;
$chart-tooltip-border-radius: var(--rz-tooltip-border-radius) !default;
$chart-tooltip-font-size: var(--rz-tooltip-font-size) !default;
$chart-tooltip-item-border-radius: var(--rz-border-radius) !default;
$chart-tooltip-item-hover-background-color: var(--rz-grid-stripe-background-color) !default;
$chart-marker-stroke: var(--rz-base-background-color) !default;

$chart-color-schemes: (
  pastel: (
    var(--rz-series-1),
    var(--rz-series-2),
    var(--rz-series-3),
    var(--rz-series-4),
    var(--rz-series-5),
    var(--rz-series-6),
    var(--rz-series-7),
    var(--rz-series-8),
    var(--rz-series-9),
    var(--rz-series-10),
    var(--rz-series-11),
    var(--rz-series-12),
    var(--rz-series-13),
    var(--rz-series-14),
    var(--rz-series-15),
    var(--rz-series-16),
    var(--rz-series-17),
    var(--rz-series-18),
    var(--rz-series-19),
    var(--rz-series-20),
    var(--rz-series-21),
    var(--rz-series-22),
    var(--rz-series-23),
    var(--rz-series-24)
  ),
  palette: (
    #003f5c,
    #2f4b7c,
    #665191,
    #a05195,
    #d45087,
    #f95d6a,
    #ff7c43,
    #ffa600
  ),
  monochrome: (
    #004c6d,
    #296080,
    #437594,
    #5d8ba9,
    #75a1be,
    #8eb8d3,
    #a7cfe9,
    #c1e7ff
  ),
  divergent: (
    #00876c,
    #57a18b,
    #8cbcac,
    #bed6ce,
    #f1f1f1,
    #f1c6c6,
    #ec9c9d,
    #e27076,
    #d43d51
  )
) !default;

.rz-chart {
  box-sizing: border-box;
  position: relative;
  height: 300px;
}

.rz-area-series {
  fill-opacity: 0.8;
}

@each $scheme, $colors in $chart-color-schemes {
  @each $color in $colors {
    $index: index($colors, $color);
    .rz-scheme-#{$scheme} {
      .rz-series-#{$index - 1} {
        fill: $color;
        stroke: $color;
      }

      .rz-series-#{$index - 1}-tooltip {
        .rz-chart-tooltip-content {
          border: 1px solid $color;
        }
      }

      .rz-series-item-#{$index - 1} {
        fill: $color;
        stroke: $color;

        .rz-chart-tooltip-content {
          border: 1px solid $color;
        }
      }
    }
  }
}

.rz-marker {
  stroke: var(--rz-chart-marker-stroke);
}

.rz-area-series .rz-marker {
  fill-opacity: 1;
}

.rz-axis {
  stroke: var(--rz-chart-axis-color);
  font-size: var(--rz-chart-axis-font-size);
}

.rz-axis .rz-grid-line {
  stroke: var(--rz-chart-axis-color);
}

.rz-tick-text {
  stroke: none;
  fill: var(--rz-chart-axis-label-color);
}

.rz-series-data-label {
  fill: var(--rz-chart-axis-label-color);
}

.rz-value-axis .rz-tick-text {
  text-anchor: end;

  /* Right-to-left anchor */
  *[dir="rtl"] & {
    text-anchor: start;
  }
}

.rz-axis .rz-axis-title {
  stroke: none;
  text-anchor: middle;
  fill: var(--rz-text-title-color)
}

.rz-donut-title {
  text-anchor: middle;
  fill: var(--rz-text-title-color)
}

.rz-donut-content {
  height: 100%;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.rz-legend {
  position: absolute;
  display: flex;
  font-size: var(--rz-chart-legend-font-size);
}

.rz-legend-right {
  right: 0;
  top: 0;
  bottom: 0;
  align-items: center;
}

.rz-legend-left {
  left: 0;
  top: 0;
  bottom: 0;
  align-items: center;
}

.rz-legend-top {
  top: 0;
  left: 0;
  right: 0;
  justify-content: center;
}

.rz-legend-bottom {
  bottom: 0;
  left: 0;
  right: 0;
  justify-content: center;
}

.rz-legend-items {
  padding: 0;
  margin: 0;
  overflow-y: auto;
  overflow-x: hidden;
  max-height: 100%;
}

.rz-legend-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  margin: 0.25rem;
  cursor: pointer;

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-chart-legend-focus-outline);
    outline-offset: var(--rz-chart-legend-focus-outline-offset);
    border-radius: var(--rz-border-radius);
  }
}

.rz-legend-top .rz-legend-item,
.rz-legend-bottom .rz-legend-item {
  display: inline-flex;
}

.rz-chart-tooltip {
  position: absolute;
  top: 0;
  left: 0;
}

.rz-top-chart-tooltip  {
  transform: translate(-50%, -100%);
}

.rz-bottom-chart-tooltip {
  transform: translate(-50%, 0);
}

.rz-chart-tooltip-content {
  background: var(--rz-chart-tooltip-background);
  color: var(--rz-chart-tooltip-color);
  box-shadow: 0 6px 14px 0 rgba(0, 0, 0, 0.15);
  padding: 0.5rem;
  border-radius: var(--rz-chart-tooltip-border-radius);
  white-space: nowrap;
  font-size: var(--rz-chart-tooltip-font-size);
}

.rz-chart-tooltip {
  &:not(.rz-pie-tooltip) {
    .rz-chart-tooltip-content {
      &:after {
        content: ' ';
        position: absolute;
        width: 8px;
        height: 8px;
        background-color: inherit;
        transform-origin: center;
      }
    }
  }
}

.rz-top-chart-tooltip {
  &:not(.rz-pie-tooltip) {
    .rz-chart-tooltip-content {
      margin-bottom: 15px;
      &:after {
        left: 50%;
        bottom: 0;
        transform: translate(-50%, -11px) rotate(45deg);
        border-bottom: inherit;
        border-right: inherit;
      }
    }
  }
}

.rz-bottom-chart-tooltip {
  &:not(.rz-pie-tooltip) {
    .rz-chart-tooltip-content {
      margin-top: 15px;
      &:after {
        left: 50%;
        top: 0;
        transform: translate(-50%, 11px) rotate(45deg);
        border-top: inherit;
        border-left: inherit;
      }
    }
  }
}

.rz-chart-tooltip-title {
  font-weight: 700;
}

.rz-chart-tooltip-item-value {
  font-weight: 600;
  text-align: end;
  font-variant-numeric: tabular-nums;
}

// Shared Tooltip Styles

.rz-chart-shared-tooltip:has(.rz-chart-tooltip-item-content) {
  .rz-chart-tooltip-title {
    padding: 0 0.5rem;
  }

  .rz-chart-tooltip-content {
    border: none;
  }
}

.rz-chart-tooltip-item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    padding: 0.125rem 0.5rem;
    border-radius: var(--rz-chart-tooltip-item-border-radius);

    &:hover {
      background-color: var(--rz-chart-tooltip-item-hover-background-color);
    }

  .rz-legend-item {
    margin: 0;
    cursor: inherit;
  }

}
