$datafilter-item-padding-inline-start: 2rem;
$datafilter-item-padding-block: 0.25rem;
$datafilter-item-indentation: 0.25rem;
$datafilter-item-path-width: 1rem;
$datafilter-item-path-height: calc(#{$datafilter-item-padding-block} + (var(--rz-input-height) / 2));
$datafilter-item-path-border: var(--rz-border-normal);
$datafilter-item-path-border-radius: calc(2 * var(--rz-border-radius));

.rz-datafilter {
    box-sizing: border-box;
    display: flex;
    flex-wrap: wrap;
    column-gap: 0.25rem;
    align-items: center;

    .rz-selectbar {
        display: inline-flex;
    }

    .rz-datafilter-group {
        position: relative;
        margin: 0.25rem 0 0;
        padding: 0;
        flex-basis: 100%;

        .rz-datafilter-group {
            margin: 0;
        }
    }

    .rz-datafilter-item {
        position: relative;
        list-style: none;
        margin: 0;
        padding-block: var(--rz-datafilter-item-padding-block);
        padding-inline: calc(var(--rz-datafilter-item-padding-inline-start) + var(--rz-datafilter-item-indentation)) 0;
        display: flex;
        flex-wrap: wrap;
        gap: 0.25rem;
        align-items: center;

        &:before {
            content: "";
            position: absolute;
            background-color: transparent;
            width: var(--rz-datafilter-item-path-width);
            inset-block-start: 0;
            inset-block-end: 0;
            inset-inline-start: calc(var(--rz-datafilter-item-padding-inline-start) / 2);
            border: 0;
            border-inline-start: var(--rz-datafilter-item-path-border);
        }

        &:after {
            content: "";
            position: absolute;
            background-color: transparent;
            width: var(--rz-datafilter-item-path-width);
            height: calc(var(--rz-datafilter-item-padding-block) + (var(--rz-input-height) / 2));
            inset-block-start: 0;
            inset-inline-start: calc(var(--rz-datafilter-item-padding-inline-start) / 2);
            border: 0;
            border-block-end: var(--rz-datafilter-item-path-border);
            border-inline-start: var(--rz-datafilter-item-path-border);
        }

        .rz-multiselect,
        .rz-dropdown {
            flex: 10rem 1;
        }
    }

    .rz-datafilter-group-item {
        &:after {
            height: calc(var(--rz-input-height) / 2);
        }
    }

    .rz-datafilter-bar {
        padding-block: 0;
        padding-inline: calc(var(--rz-datafilter-item-padding-inline-start) + var(--rz-datafilter-item-indentation)) 0;
        &:before {
            display: none;
        }

        &:after {
            height: calc(var(--rz-input-height) / 2);
            border-end-start-radius: var(--rz-datafilter-item-path-border-radius);
        }

        .rz-splitbutton {
            margin-block-start: 0.3125rem;
        }
    }

    .rz-datafilter-editor {
        flex: 10rem 4;
    }

    .rz-button.rz-datafilter-item-clear {
        color: var(--rz-text-secondary-color);
        opacity: 0.5;

        &:hover {
            opacity: 1;
        }
    }
}
