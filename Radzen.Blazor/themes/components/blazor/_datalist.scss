$datalist-background-color: var(--rz-base-background-color) !default;
$datalist-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01) !default;
$datalist-padding: 0.5rem !default;
$datalist-border: var(--rz-border-base-200) !default;
$datalist-border-radius: var(--rz-border-radius) !default;
$datalist-item-shadow: 0 8px 10px 0 rgba(0, 0, 0, 0.01) !default;
$datalist-item-border: var(--rz-border-base-200) !default;
$datalist-item-margin-block: 0.5rem !default;
$datalist-item-margin-inline: 0.5rem !default;
$datalist-item-padding: 1rem !default;
$datalist-item-background-color: var(--rz-base-background-color) !default;

.rz-datalist,
.rz-datagrid {
  background-color: var(--rz-datalist-background-color);
  border-end-start-radius: var(--rz-datalist-border-radius);
  border-end-end-radius: var(--rz-datalist-border-radius);
  box-shadow: var(--rz-datalist-shadow);
  border: var(--rz-datalist-border);
}

%datalist-item {
  border-radius: var(--rz-datalist-border-radius);
  border: var(--rz-datalist-item-border);
  box-shadow: var(--rz-datalist-item-shadow);
  padding: var(--rz-datalist-item-padding);
  background-color: var(--rz-datalist-item-background-color);
}

.rz-datalist-data {
  list-style: none;
  padding: var(--rz-datalist-padding);
  margin: 0;

  > li {
    @extend %datalist-item;

    margin-block: var(--rz-datalist-item-margin-block);
    margin-inline: var(--rz-datalist-item-margin-inline);

    &:first-child {
      margin-block-start: 0;
    }

    &:last-child {
      margin-block-end: 0;
    }
  }
}

.rz-g {
  display: flex;
  flex-wrap: wrap;

  > div {
    @extend %datalist-item;
    flex: auto;
    margin-block: var(--rz-datalist-item-margin-block);
    margin-inline: var(--rz-datalist-item-margin-inline);
  }
}

.rz-datalist-content {
    box-sizing: border-box;
    position: relative;
}