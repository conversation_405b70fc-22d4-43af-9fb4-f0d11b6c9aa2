$upload-button-bar-background-color: var(--rz-base-200) !default;
$upload-button-bar-padding: 0.5rem !default;
$upload-button-bar-border-radius: var(--rz-border-radius) !default;
$upload-files-background-color: var(--rz-base-background-color) !default;
$upload-files-padding: 0.5rem 0 !default;
$upload-files-remove-background-color: var(--rz-base-300) !default;
$upload-files-remove-color: var(--rz-text-color) !default;
$upload-files-margin: 0 0.5rem !default;
$upload-choose-background-color: var(--rz-secondary) !default;
$upload-choose-color: var(--rz-on-secondary) !default;
$upload-choose-hover-background-color: var(--rz-secondary) !default;
$upload-choose-hover-color: var(--rz-on-secondary) !default;
$upload-choose-active-background-color: var(--rz-secondary-dark) !default;
$upload-choose-active-color: var(--rz-on-secondary-dark) !default;
$upload-cancel-background-color: var(--rz-base-200) !default;
$upload-cancel-color: var(--rz-text-color) !default;
$upload-button-background-color: var(--rz-primary) !default;

.rz-fileupload {
  box-sizing: border-box;
  display: inline-block;
  border-radius: var(--rz-upload-button-bar-border-radius);

  .rz-button {
    @extend %button-sm;
    vertical-align: middle;
    -webkit-appearance: none !important;
  }
}

.rz-fileupload-choose {
  position: relative;
  display: inline-block;
  overflow: hidden;
  background-color: var(--rz-upload-choose-background-color);
  color: var(--rz-upload-choose-color);

  input[type='file'] {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    opacity: 0;
    min-height: 100%;
    cursor: pointer;
  }

  &.rz-state-disabled {
    input[type='file'] {
      cursor: default;
    }
  }

  &:not(.rz-state-disabled) {
    background-color: var(--rz-upload-choose-background-color);
    color: var(--rz-upload-choose-color);

    &:hover {
      background-color: var(--rz-upload-choose-hover-background-color);
      color: var(--rz-upload-choose-hover-color);

      &:not(:active) {
        background-color: var(--rz-upload-choose-hover-background-color);
        color: var(--rz-upload-choose-hover-color);
      }
    }

    &:active {
      background-color: var(--rz-upload-choose-active-background-color);
      color: var(--rz-upload-choose-active-color);
    }
  }
}

.rz-fileupload-row {
  display: flex;
  align-items: center;
  justify-content: space-between;

  > div {
    margin: var(--rz-upload-files-margin);
  }

  .rz-button-text {
    display: none;
  }

  .rz-button {
    background-color: var(--rz-upload-files-remove-background-color);
    color: var(--rz-upload-files-remove-color);
    
    .rzi-close,
    .rzi-times,
    .rz-icon-trash {
      @extend %rzi;
      display: block;

      &:before {
        content: 'close';
      }
    }
  }
}

.rz-fileupload-buttonbar {
  position: relative;
  background-color: var(--rz-upload-button-bar-background-color);
  padding: var(--rz-upload-button-bar-padding);
  border-radius: var(--rz-upload-button-bar-border-radius);

  .rz-button {
    &:nth-child(3) {
      float: right;

      background-color: var(--rz-upload-cancel-background-color);
      color: var(--rz-upload-cancel-color);
    }
  }

  // Upload in a FormField
  .rz-form-field & {
    background-color: inherit;
  }

  .rz-form-field.rz-variant-flat &,
  .rz-form-field.rz-variant-filled & {
    --rz-upload-button-bar-padding: calc(var(--rz-form-field-label-floating-top) + 1.5rem) calc(var(--rz-form-field-label-inset-inline-start) + 0.25rem) calc(var(--rz-form-field-label-inset-inline-start) + 0.25rem);
  }
}

.rz-fileupload-files {
  background-color: var(--rz-upload-files-background-color);
  padding: var(--rz-upload-files-padding);

  // Files in a FormField
  .rz-form-field & {
    background-color: inherit;
  }
}
