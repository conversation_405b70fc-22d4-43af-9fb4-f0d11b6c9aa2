$lookup-panel-background-color: var(--rz-base-background-color) !default;
$lookup-panel-padding: 1rem !default;
$lookup-search-gap: 0.5rem !default;
$lookup-search-margin-bottom: 0.5rem !default;

.rz-lookup-panel {
  background-color: var(--rz-lookup-panel-background-color);
  padding: var(--rz-lookup-panel-padding);
  container-type: inline-size;
  container-name: rz-lookup-panel;
}

.rz-lookup-search {
  display: flex;
  gap: var(--rz-lookup-search-gap);
  margin-bottom: var(--rz-lookup-search-margin-bottom);

  input {
    flex: auto;
    @extend %input;
  }
}
