$dialog-background-color: var(--rz-base-background-color) !default;
$dialog-shadow: 0 22px 64px 0 rgba(0, 0, 0, 0.22) !default;
$dialog-title-background-color: var(--rz-base-100) !default;
$dialog-title-border: none !default;
$dialog-title-padding-block: 0.6875rem!default;
$dialog-title-padding-inline: 1.25rem !default;
$dialog-title-font-size: 1.25rem !default;
$dialog-title-line-height: 1.875rem !default;
$dialog-title-font-weight: 700 !default;
$dialog-title-letter-spacing: -0.03em !default;
$dialog-title-color: var(--rz-text-title-color) !default;
$dialog-close-font-size: var(--rz-icon-size) !default;
$dialog-close-color: var(--rz-text-tertiary-color) !default;
$dialog-close-hover-color: var(--rz-text-color) !default;
$dialog-close-vertical-align: middle !default;
$dialog-content-padding: 1.25rem !default;
$dialog-mask-background-color: var(--rz-base-800) !default;
$dialog-border-radius: var(--rz-border-radius) !default;
$dialog-mask-zindex: 1000 !default;
$dialog-zindex: 1001 !default;
$dialog-transition: .1s ease-in-out !default;

.rz-dialog-wrapper {
  box-sizing: border-box;
  display: flex;
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  bottom: 0;
  z-index: var(--rz-dialog-zindex);
  align-items: center;
  justify-content: center;
}

.rz-dialog {
  box-sizing: border-box;
  position: absolute;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 150px;
  max-height: 100%;
  min-height: 150px;
  z-index: var(--rz-dialog-zindex);
  background-color: var(--rz-dialog-background-color);
  box-shadow: var(--rz-dialog-shadow);
  border-radius: var(--rz-dialog-border-radius);
}

.rz-dialog-side {
  box-sizing: border-box;
  position: fixed;
  z-index: var(--rz-dialog-zindex);
  background-color: var(--rz-dialog-background-color);
  box-shadow: var(--rz-dialog-shadow);
  border-radius: var(--rz-dialog-border-radius);
  overflow-y: auto;
}

.rz-dialog-titlebar,
.rz-dialog-side-titlebar {
  display: flex;
  align-items: normal;
  padding-block: var(--rz-dialog-title-padding-block);
  padding-inline: var(--rz-dialog-title-padding-inline);
  font-size: var(--rz-dialog-title-font-size);
  line-height: var(--rz-dialog-title-line-height);
  background-color: var(--rz-dialog-title-background-color);
  border-block-end: var(--rz-dialog-title-border);
}

.rz-dialog-title,
.rz-dialog-side-title {
  flex: auto;
  font-weight: var(--rz-dialog-title-font-weight);
  letter-spacing: var(--rz-dialog-title-letter-spacing);
  color: var(--rz-dialog-title-color);
  user-select: none;
}

.rz-dialog-titlebar-close,
.rz-dialog-side-titlebar-close {
  .rzi-times {
    font-size: var(--rz-dialog-close-font-size);
    color: var(--rz-dialog-close-color);
    vertical-align: var(--rz-dialog-close-vertical-align);

    &:before {
      content: 'close';
    }
  }

  &:hover .rzi-times {
    color: var(--rz-dialog-close-hover-color);
  }
}

.rz-dialog-content,
.rz-dialog-side-content {
  flex: 0 1 auto;
  padding: var(--rz-dialog-content-padding);
  overflow: auto;
}

.rz-dialog-confirm, .rz-dialog-alert {
  max-width: 400px;
  margin: 0.75rem;
}

.rz-dialog-confirm-message, .rz-dialog-alert-message {
  margin-block-end: 1.5rem;
}

.rz-dialog-confirm-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;

  .rz-button {
    flex: 8rem 1;
    min-width: fit-content;
  }
}

.rz-dialog-alert-buttons {
    text-align: end;
    gap: 0.5rem;

    .rz-button {
        flex: 8rem 1;
        min-width: fit-content;
    }
}

.rz-dialog-mask {
  position: fixed;
  z-index: var(--rz-dialog-mask-zindex);
  width: 100%;
  height: 100%;
  inset-block-start: 0;
  inset-inline-start: 0;
  background-color: var(--rz-dialog-mask-background-color);
  opacity: 0.5;
}

.no-scroll {
  overflow: hidden;
  padding-inline-end: 15px;
}

.rz-dialog-side-position-right {
  right: 0;
  min-width: 150px;
  width: 400px;
  max-width: 100%;
  height: 100%;
}

.rz-dialog-side-position-left {
  left: 0;
  min-width: 150px;
  width: 400px;
  max-width: 100%;
  height: 100%;
}

.rz-dialog-side-position-top {
  top: 0;
  min-height: 150px;
  height: 250px;
  width: 100%;
}

.rz-dialog-side-position-bottom {
  bottom: 0;
  min-height: 150px;
  height: 250px;
  width: 100%;
}


// Dialog Animations

@keyframes rz-dialog-open-scale {
  from {
    scale: 0.5;
  }
  to {
    scale: 1;
  }
}

@keyframes rz-dialog-open-opacity {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.rz-dialog {
  &.rz-open {
    animation: var(--rz-dialog-transition) rz-dialog-open-opacity, var(--rz-dialog-transition) rz-dialog-open-scale;
  }

  &:has(.rz-chart).rz-open {
    animation: var(--rz-dialog-transition) rz-dialog-open-opacity;
  }
}

@keyframes rz-side-dialog-right-open {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes rz-side-dialog-right-close {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(100%);
  }
}

.rz-dialog-side-position-right {
  &.rz-open {
    animation: var(--rz-dialog-transition) rz-side-dialog-right-open;
  }

  &.rz-close {
    animation: var(--rz-dialog-transition) rz-side-dialog-right-close;
    transform: translateX(100%);
  }
}

@keyframes rz-side-dialog-top-open {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes rz-side-dialog-top-close {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(-100%);
  }
}

.rz-dialog-side-position-top {
  &.rz-open {
    animation: var(--rz-dialog-transition) rz-side-dialog-top-open;
  }

  &.rz-close {
    animation: var(--rz-dialog-transition) rz-side-dialog-top-close;
    transform: translateX(-100%);
  }
}

@keyframes rz-side-dialog-bottom-open {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

@keyframes rz-side-dialog-bottom-close {
  from {
    transform: translateY(0);
  }
  to {
    transform: translateY(100%);
  }
}

.rz-dialog-side-position-bottom {
  &.rz-open {
    animation: var(--rz-dialog-transition) rz-side-dialog-bottom-open;
  }

  &.rz-close {
    animation: var(--rz-dialog-transition) rz-side-dialog-bottom-close;
    transform: translateY(100%);
  }
}

@keyframes rz-side-dialog-left-open {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes rz-side-dialog-left-close {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

.rz-dialog-side-position-left {
  &.rz-open {
    animation: var(--rz-dialog-transition) rz-side-dialog-left-open;
  }

  &.rz-close {
    animation: var(--rz-dialog-transition) rz-side-dialog-left-close;
    transform: translateX(-100%);
  }
}

// Dialog Responsive Styles

@media (max-width: 768px) {
  .rz-dialog {
    &:not(.rz-dialog-confirm):not(.rz-dialog-alert) {
      position: absolute;
      width: 100% !important;
      inset-block-start: 0px !important;
      border-radius: 0;
    }
  }

  .rz-dialog-content {
    -webkit-overflow-scrolling: touch;
  }

  .rz-dialog-confirm, .rz-dialog-alert {
    max-width: 355px;
  }

  .no-scroll {
    padding-inline-end: 0;
  }
}