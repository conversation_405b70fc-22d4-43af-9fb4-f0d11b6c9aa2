$switch-width: 3rem !default;
$switch-height: round($switch-width*600)/1000 !default;
$switch-circle-size: round($switch-width*420)/1000 !default;
$switch-circle-offset: round(($switch-height - $switch-circle-size)*500)/1000 !default;
$switch-background-color: var(--rz-base-400) !default;
$switch-checked-background-color: var(--rz-secondary) !default;
$switch-box-shadow: none !default;
$switch-circle-background-color: var(--rz-text-contrast-color) !default;
$switch-checked-circle-background-color: var(--rz-on-secondary) !default;
$switch-focus-outline: var(--rz-outline-focus) !default;
$switch-focus-outline-offset: var(--rz-outline-offset) !default;

.rz-switch {
  box-sizing: border-box;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  width: $switch-width;
  min-width: $switch-width;
  height: $switch-height;
  border-radius: calc(5 * var(--rz-border-radius));

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-switch-focus-outline);
    outline-offset: var(--rz-switch-focus-outline-offset);
  }
}

.rz-switch-circle {
  position: absolute;
  cursor: pointer;
  inset-block-start: 0;
  inset-inline-start: 0;
  inset-inline-end: 0;
  inset-block-end: 0;
}

.rz-switch-circle.rz-disabled {
  opacity: 0.5;
  cursor: initial;
}

.rz-switch-circle.rz-readonly {
  cursor: initial;
}

.rz-switch .rz-switch-circle {
  background: var(--rz-switch-background-color);
  transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s;
  border-radius: calc(5 * var(--rz-border-radius));
}

.rz-switch.rz-switch-checked .rz-switch-circle {
  background: var(--rz-switch-checked-background-color);
}

.rz-switch .rz-switch-circle:before {
  background: var(--rz-switch-circle-background-color);
  width: $switch-circle-size;
  height: $switch-circle-size;
  inset-inline-start: $switch-circle-offset;
  margin-block-start: round($switch-circle-size*-500)/1000;
  border-radius: calc((5 * var(--rz-border-radius)) - 3px);
  transition: transform 0.2s linear;
  box-shadow: var(--rz-switch-box-shadow);
}

.rz-switch-circle:before {
  position: absolute;
  content: "";
  inset-block-start: 50%;
}

.rz-switch-circle.rz-disabled:before {
  box-shadow: none;
}

.rz-switch.rz-switch-checked .rz-switch-circle:before {
  background: var(--rz-switch-checked-circle-background-color);
  transform: translateX($switch-width - ($switch-circle-size + $switch-circle-offset*2));
}

*[dir="rtl"] .rz-switch.rz-switch-checked .rz-switch-circle:before {
  transform: translateX(-1 * ($switch-width - ($switch-circle-size + $switch-circle-offset*2)));
}
