$selectbar-background-color: var(--rz-base-backgorund-color) !default;
$selectbar-color: var(--rz-text-color) !default;
$selectbar-border: var(--rz-border-normal) !default;
$selectbar-selected-background-color: var(--rz-secondary) !default;
$selectbar-selected-color: var(--rz-on-secondary) !default;
$selectbar-selected-border: var(--rz-border-secondary) !default;
$selectbar-border-radius: var(--rz-border-radius) !default;
$selectbar-focus-outline: var(--rz-outline-focus) !default;
$selectbar-focus-outline-offset: var(--rz-outline-offset) !default;
$selectbar-button-focus-outline: var(--rz-outline-width) solid var(--rz-text-color) !default;
$selectbar-button-focus-outline-offset: calc(-1 * var(--rz-outline-width)) !default;
$selectbar-sizes: xs, sm, md, lg;

.rz-selectbar {
  box-sizing: border-box;
  display: inline-flex;
  border-radius: var(--rz-selectbar-border-radius);

  &:focus {
    outline: var(--rz-outline-normal);
  }

  &:focus-visible {
    outline: var(--rz-selectbar-focus-outline);
    outline-offset: var(--rz-selectbar-focus-outline-offset);

    .rz-button.rz-state-focused:not(.rz-state-disabled) {
      outline: var(--rz-selectbar-button-focus-outline);
      outline-offset: var(--rz-selectbar-button-focus-outline-offset);
      z-index: 1;
    }
  }

  .rz-button {
    display: inline-block;
    background-color: var(--rz-selectbar-background-color);
    color: var(--rz-selectbar-color);
    border: var(--rz-selectbar-border);
    border-radius: 0;
  
    &.rz-state-active {
      background-color: var(--rz-selectbar-selected-background-color);
      color: var(--rz-selectbar-selected-color);
      border: var(--rz-selectbar-selected-border);
    }
  }

  &.rz-selectbar-horizontal {
    flex-direction: row;

    .rz-button {
      &:first-child {
        border-start-start-radius: var(--rz-selectbar-border-radius);
        border-end-start-radius: var(--rz-selectbar-border-radius);
      }

      &:not(:first-child) {
        border-inline-start: none;
      }
    
      &:last-child {
        border-start-end-radius: var(--rz-selectbar-border-radius);
        border-end-end-radius: var(--rz-selectbar-border-radius);
      }
    }
  }

  &.rz-selectbar-vertical {
    flex-direction: column;

    .rz-button {
      text-align: center;

      &:first-child {
        border-top-left-radius: var(--rz-selectbar-border-radius);
        border-top-right-radius: var(--rz-selectbar-border-radius);
      }

      &:not(:first-child) {
        border-top: none;
      }
    
      &:last-child {
        border-bottom-left-radius: var(--rz-selectbar-border-radius);
        border-bottom-right-radius: var(--rz-selectbar-border-radius);
      }
    }
  }
}
