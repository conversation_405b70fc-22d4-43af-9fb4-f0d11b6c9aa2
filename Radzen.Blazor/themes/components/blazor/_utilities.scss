// Box Sizing

[class^='rz-'],
[class*=' rz-'] {
  &,
  &::before,
  &::after,
  & *,
  & *::before,
  & *::after {
    box-sizing: border-box;
  }
}

// Theme Colors

$rz-theme-colors-map: () !default;
$rz-theme-colors-map: map-merge(
  (
    "white": $rz-white,
    "black": $rz-black,
    
    "base": $rz-base,
    "base-50": $rz-base-50,
    "base-100": $rz-base-100,
    "base-200": $rz-base-200,
    "base-300": $rz-base-300,
    "base-400": $rz-base-400,
    "base-500": $rz-base-500,
    "base-600": $rz-base-600,
    "base-700": $rz-base-700,
    "base-800": $rz-base-800,
    "base-900": $rz-base-900,
    "base-light": $rz-base-light,
    "base-lighter": $rz-base-lighter,
    "base-dark": $rz-base-dark,
    "base-darker": $rz-base-darker,
    
    "primary": $rz-primary,
    "primary-light": $rz-primary-light,
    "primary-lighter": $rz-primary-lighter,
    "primary-dark": $rz-primary-dark,
    "primary-darker": $rz-primary-darker,
    
    "secondary": $rz-secondary,
    "secondary-light": $rz-secondary-light,
    "secondary-lighter": $rz-secondary-lighter,
    "secondary-dark": $rz-secondary-dark,
    "secondary-darker": $rz-secondary-darker,
    
    "info": $rz-info,
    "info-light": $rz-info-light,
    "info-lighter": $rz-info-lighter,
    "info-dark": $rz-info-dark,
    "info-darker": $rz-info-darker,
    
    "success": $rz-success,
    "success-light": $rz-success-light,
    "success-lighter": $rz-success-lighter,
    "success-dark": $rz-success-dark,
    "success-darker": $rz-success-darker,
    
    "warning": $rz-warning,
    "warning-light": $rz-warning-light,
    "warning-lighter": $rz-warning-lighter,
    "warning-dark": $rz-warning-dark,
    "warning-darker": $rz-warning-darker,
    
    "danger": $rz-danger,
    "danger-light": $rz-danger-light,
    "danger-lighter": $rz-danger-lighter,
    "danger-dark": $rz-danger-dark,
    "danger-darker": $rz-danger-darker,

    "on-base": $rz-base-900,
    "on-base-light": $rz-base-900,
    "on-base-lighter": $rz-base-900,
    "on-base-dark": $rz-base-50,
    "on-base-darker": $rz-base-50,

    "on-primary": $rz-white,
    "on-primary-light": $rz-white,
    "on-primary-lighter": $rz-primary,
    "on-primary-dark": $rz-white,
    "on-primary-darker": $rz-white,
    
    "on-secondary": $rz-white,
    "on-secondary-light": $rz-white,
    "on-secondary-lighter": $rz-secondary,
    "on-secondary-dark": $rz-white,
    "on-secondary-darker": $rz-white,
    
    "on-info": $rz-white,
    "on-info-light": $rz-white,
    "on-info-lighter": $rz-info,
    "on-info-dark": $rz-white,
    "on-info-darker": $rz-white,
    
    "on-success": $rz-white,
    "on-success-light": $rz-white,
    "on-success-lighter": $rz-success,
    "on-success-dark": $rz-white,
    "on-success-darker": $rz-white,
    
    "on-warning": $rz-white,
    "on-warning-light": $rz-white,
    "on-warning-lighter": $rz-warning,
    "on-warning-dark": $rz-white,
    "on-warning-darker": $rz-white,
    
    "on-danger": $rz-white,
    "on-danger-light": $rz-white,
    "on-danger-lighter": $rz-danger,
    "on-danger-dark": $rz-white,
    "on-danger-darker": $rz-white,
    
    "series-1": $rz-series-1,
    "series-2": $rz-series-2,
    "series-3": $rz-series-3,
    "series-4": $rz-series-4,
    "series-5": $rz-series-5,
    "series-6": $rz-series-6,
    "series-7": $rz-series-7,
    "series-8": $rz-series-8,
    "series-9": $rz-series-9,
    "series-10": $rz-series-10,
    "series-11": $rz-series-11,
    "series-12": $rz-series-12,
    "series-13": $rz-series-13,
    "series-14": $rz-series-14,
    "series-15": $rz-series-15,
    "series-16": $rz-series-16,
    "series-17": $rz-series-17,
    "series-18": $rz-series-18,
    "series-19": $rz-series-19,
    "series-20": $rz-series-20,
    "series-21": $rz-series-21,
    "series-22": $rz-series-22,
    "series-23": $rz-series-23,
    "series-24": $rz-series-24,
  ),
  $rz-theme-colors-map
);

// Theme Constants

$rz-theme-constants-map: () !default;
$rz-theme-constants-map: map-merge(
  (
    "border-width": $rz-border-width,
    "root-font-size": $rz-root-font-size,
    "body-font-size": $rz-body-font-size,
    "body-line-height": $rz-body-line-height,
    "body-background-color": $rz-body-background-color,
    "text-font-family": $rz-text-font-family,
    "outline-offset": $rz-outline-offset,
    "outline-width": $rz-outline-width,
    "outline-color": $rz-outline-color,
  ),
  $rz-theme-constants-map
);

// Semantic Text Color

$rz-text-title-color: var(--rz-base-900) !default;
$rz-text-color: var(--rz-base-800) !default;
$rz-text-secondary-color: var(--rz-base-700) !default;
$rz-text-tertiary-color: var(--rz-base-600) !default;
$rz-text-disabled-color: var(--rz-base-500) !default;
$rz-text-contrast-color: var(--rz-white) !default;

$rz-text-map: () !default;
$rz-text-map: map-merge(
  (
    "text-title-color": $rz-text-title-color,
    "text-color": $rz-text-color,
    "text-secondary-color": $rz-text-secondary-color,
    "text-tertiary-color": $rz-text-tertiary-color,
    "text-disabled-color": $rz-text-disabled-color,
    "text-contrast-color": $rz-text-contrast-color,
  ),
  $rz-text-map
);

// Semantic Text Color CSS classes
@each $token, $value in $rz-text-map {
  .rz-#{$token} {
    color: #{$value} !important;
  }
}

// Link Color

$rz-link-color: var(--rz-secondary) !default;
$rz-link-text-decoration: none !default;
$rz-link-hover-color: var(--rz-secondary-dark) !default;
$rz-link-hover-text-decoration: underline !default;
$rz-link-focus-outline: var(--rz-outline-focus) !default;
$rz-link-focus-outline-offset: var(--rz-outline-offset) !default;

$rz-link-map: () !default;
$rz-link-map: map-merge(
  (
    "link-color": $rz-link-color,
    "link-text-decoration": $rz-link-text-decoration,
    "link-hover-color": $rz-link-hover-color,
    "link-hover-text-decoration": $rz-link-hover-text-decoration,
    "link-focus-outline": $rz-link-focus-outline,
    "link-focus-outline-offset": $rz-link-focus-outline-offset,
  ),
  $rz-link-map
);

// Background Color

$rz-base-background-color: var(--rz-white) !default;

// Base/Light/Dark Styles Map 

$base-styles-map: () !default;
$base-styles-map: map-merge(
  (
    base: (
      background-color: var(--rz-base-200),
      color: var(--rz-text-color)
    ),
    light: (
      background-color: var(--rz-base-200),
      color: var(--rz-text-color)
    ),
    dark: (
      background-color: var(--rz-base-900),
      color: var(--rz-text-contrast-color)
    )
  ),
  $base-styles-map
);

// Severity Styles Map

$severity-styles-map: () !default;
$severity-styles-map: map-merge(
  (
    primary: (
      background-color: var(--rz-primary),
      color: var(--rz-on-primary)
    ),
    secondary: (
      background-color: var(--rz-secondary),
      color: var(--rz-on-secondary)
    ),
    info: (
      background-color: var(--rz-info),
      color: var(--rz-on-info)
    ),
    warning: (
      background-color: var(--rz-warning),
      color: var(--rz-on-warning)
    ),
    error: (
      background-color: var(--rz-danger),
      color: var(--rz-on-danger)
    ),
    danger: (
      background-color: var(--rz-danger),
      color: var(--rz-on-danger)
    ),
    success: (
      background-color: var(--rz-success),
      color: var(--rz-on-success)
    )
  ),
  $severity-styles-map
);


// Border

// Interaction State Border
$rz-border-normal: var(--rz-border-width) solid var(--rz-base-300) !default;
$rz-border-hover: var(--rz-border-width) solid var(--rz-base-400) !default;
$rz-border-focus: var(--rz-border-width) solid var(--rz-base-400) !default;
$rz-border-disabled: var(--rz-border-width) solid var(--rz-base-200) !default;

$rz-state-border-map: () !default;
$rz-state-border-map: map-merge(
  (
    "border-normal": $rz-border-normal,
    "border-hover": $rz-border-hover,
    "border-focus": $rz-border-focus,
    "border-disabled": $rz-border-disabled,
  ),
  $rz-state-border-map
);

// Border Radius

$rz-border-radius-0: 0 !default;
$rz-border-radius-1: 0.25rem !default;
$rz-border-radius-2: 0.5rem !default;
$rz-border-radius-3: 0.75rem !default;
$rz-border-radius-4: 1rem !default;
$rz-border-radius-5: 1.25rem !default;
$rz-border-radius-6: 1.5rem !default;
$rz-border-radius-7: 1.75rem !default;
$rz-border-radius-8: 2rem !default;
$rz-border-radius-9: 2.25rem !default;
$rz-border-radius-10: 2.5rem !default;

$rz-border-radius-map: () !default;
$rz-border-radius-map: map-merge(
  (
    "border-radius": $rz-border-radius,
    "border-radius-0": $rz-border-radius-0,
    "border-radius-1": $rz-border-radius-1,
    "border-radius-2": $rz-border-radius-2,
    "border-radius-3": $rz-border-radius-3,
    "border-radius-4": $rz-border-radius-4,
    "border-radius-5": $rz-border-radius-5,
    "border-radius-6": $rz-border-radius-6,
    "border-radius-7": $rz-border-radius-7,
    "border-radius-8": $rz-border-radius-8,
    "border-radius-9": $rz-border-radius-9,
    "border-radius-10": $rz-border-radius-10,
  ),
  $rz-border-radius-map
);

// Border Radius CSS classes
@include rz-utility-map-css('border-radius', $rz-border-radius-map);


// Outline

// Interaction State Outline
$rz-outline-normal: none !default;
$rz-outline-focus: var(--rz-outline-width) solid var(--rz-outline-color) !default;

$rz-state-outline-map: () !default;
$rz-state-outline-map: map-merge(
  (
    "outline-normal": $rz-outline-normal,
    "outline-focus": $rz-outline-focus,
  ),
  $rz-state-outline-map
);


// Shadow

$rz-shadow-0: none !default;
$rz-shadow-1: 0 0 2px 0 rgba(0, 0, 0, 0.08), 0px 2px 2px rgba(0, 0, 0, 0.08) !default;
$rz-shadow-2: 0 0 2px 0 rgba(0, 0, 0, 0.08), 0px 2px 2px rgba(0, 0, 0, 0.08), 0px 4px 12px 0 rgba(0, 0, 0, 0.04)  !default;
$rz-shadow-3: 0 0 4px 0 rgba(0, 0, 0, 0.04), 0px 2px 4px rgba(0, 0, 0, 0.04), 0px 4px 16px rgba(0, 0, 0, 0.10) !default;
$rz-shadow-4: 0 0 4px 0 rgba(0, 0, 0, 0.04), 0px 3px 5px rgba(0, 0, 0, 0.04), 0px 8px 20px rgba(0, 0, 0, 0.11) !default;
$rz-shadow-5: 0 0 6px 0 rgba(0, 0, 0, 0.04), 0px 4px 6px rgba(0, 0, 0, 0.04), 0px 12px 24px rgba(0, 0, 0, 0.12) !default;
$rz-shadow-6: 0 0 6px 0 rgba(0, 0, 0, 0.04), 0px 5px 8px rgba(0, 0, 0, 0.04), 0px 14px 28px rgba(0, 0, 0, 0.13) !default;
$rz-shadow-7: 0 0 8px 0 rgba(0, 0, 0, 0.04), 0px 6px 10px rgba(0, 0, 0, 0.04), 0px 16px 32px rgba(0, 0, 0, 0.14) !default;
$rz-shadow-8: 0 0 8px 0 rgba(0, 0, 0, 0.04), 0px 7px 12px rgba(0, 0, 0, 0.04), 0px 18px 40px rgba(0, 0, 0, 0.15) !default;
$rz-shadow-9: 0 0 12px 0 rgba(0, 0, 0, 0.04), 0px 8px 16px rgba(0, 0, 0, 0.04), 0px 20px 48px rgba(0, 0, 0, 0.16) !default;
$rz-shadow-10: 0 0 12px 0 rgba(0, 0, 0, 0.04), 0px 9px 20px rgba(0, 0, 0, 0.04), 0px 22px 64px rgba(0, 0, 0, 0.20) !default;

$rz-shadow-map: () !default;
$rz-shadow-map: map-merge(
  (
    "shadow-0": $rz-shadow-0,
    "shadow-1": $rz-shadow-1,
    "shadow-2": $rz-shadow-2,
    "shadow-3": $rz-shadow-3,
    "shadow-4": $rz-shadow-4,
    "shadow-5": $rz-shadow-5,
    "shadow-6": $rz-shadow-6,
    "shadow-7": $rz-shadow-7,
    "shadow-8": $rz-shadow-8,
    "shadow-9": $rz-shadow-9,
    "shadow-10": $rz-shadow-10,
  ),
  $rz-shadow-map
);

// Shadow CSS classes
@include rz-utility-map-css('box-shadow', $rz-shadow-map);

// Transition

$rz-transition: 0.1s linear !default;
$rz-transition-all: all 0.1s linear !default;

// Layout

// Responsive breakpoints map
$rz-breakpoints-map: () !default;
$rz-breakpoints-map: map-merge(
  (
    xs: 576px,
    sm: 768px,
    md: 1024px,
    lg: 1280px,
    xl: 1920px,
    xx: 2560px,
  ),
  $rz-breakpoints-map
);

// Gutters
$rz-gutter: 1rem;
$rz-gutter-map: () !default;
$rz-gutter-map: map-merge(
  (
    0: 0,
    "05": ($rz-gutter * .125),
    1: ($rz-gutter * .25),
    2: ($rz-gutter * .5),
    3: ($rz-gutter * .75),
    4: $rz-gutter,
    5: ($rz-gutter * 1.25),
    6: ($rz-gutter * 1.5),
    7: ($rz-gutter * 1.75),
    8: ($rz-gutter * 2),
    9: ($rz-gutter * 2.25),
    10: ($rz-gutter * 2.5),
    11: ($rz-gutter * 2.75),
    12: ($rz-gutter * 3)
  ),
  $rz-gutter-map
);

// Display
// Example .rz-display-block
$display: none, block, inline, inline-block, flex, inline-flex, grid, inline-grid;
@include rz-utility-list-css('display', $display);
@include rz-utility-list-breakpoints-css('display', $display, $rz-breakpoints-map);

// Justify-content
// Example .rz-justify-content-center
$justify-content: normal, stretch, center, start, end, flex-start, flex-end, left, right, space-between, space-around, space-evenly;
@include rz-utility-list-css('justify-content', $justify-content);
@include rz-utility-list-breakpoints-css('justify-content', $justify-content, $rz-breakpoints-map);

// Align-items
// Example .rz-align-items-center
$align-items: normal, stretch, center, start, end, flex-start, flex-end;
@include rz-utility-list-css('align-items', $align-items);
@include rz-utility-list-breakpoints-css('align-items', $align-items, $rz-breakpoints-map);

// Overflow
// Example .rz-overflow-auto
$overflow: auto, scroll, visible, hidden;
@include rz-utility-list-css('overflow', $overflow);
@include rz-utility-list-breakpoints-css('overflow', $overflow, $rz-breakpoints-map);

// Width Percentage
// Example .rz-w-25
$width: 25, 50, 75, 100;
@include rz-utility-list-css('width', $width, 'w', 'percent');
@include rz-utility-list-breakpoints-css('width', $width, $rz-breakpoints-map, 'w', 'percent');

// Width Viewport
// Example .rz-vw-25
@include rz-utility-list-css('width', $width, 'vw', 'vw');
@include rz-utility-list-breakpoints-css('width', $width, $rz-breakpoints-map, 'vw', 'vw');

// Width Keywords
// Example .rz-w-auto
$width-k: auto, fit-content, min-content, max-content, stretch;
@include rz-utility-list-css('width', $width-k, 'w');
@include rz-utility-list-breakpoints-css('width', $width-k, $rz-breakpoints-map, 'w');

// Min-width Percentage
// Example .rz-min-w-25
@include rz-utility-list-css('min-width', $width, 'min-w', 'percent');
@include rz-utility-list-breakpoints-css('min-width', $width, $rz-breakpoints-map, 'min-w', 'percent');

// Max-width Percentage
// Example .rz-max-w-25
@include rz-utility-list-css('max-width', $width, 'max-w', 'percent');
@include rz-utility-list-breakpoints-css('max-width', $width, $rz-breakpoints-map, 'max-w', 'percent');

// Height Percentage
// Example .rz-h-25
$height: 25, 50, 75, 100;
@include rz-utility-list-css('height', $height, 'h', 'percent');
@include rz-utility-list-breakpoints-css('height', $height, $rz-breakpoints-map, 'h', 'percent');

// Height Viewport
// Example .rz-vh-25
@include rz-utility-list-css('height', $height, 'vh', 'vh');
@include rz-utility-list-breakpoints-css('height', $height, $rz-breakpoints-map, 'vh', 'vh');

// Height Keywords
// Example .rz-h-auto
$height-k: auto;
@include rz-utility-list-css('height', $height-k, 'h');
@include rz-utility-list-breakpoints-css('height', $height-k, $rz-breakpoints-map, 'h');

// Min-height Percentage
// Example .rz-min-h-25
@include rz-utility-list-css('min-height', $height, 'min-h', 'percent');
@include rz-utility-list-breakpoints-css('min-height', $height, $rz-breakpoints-map, 'min-h', 'percent');

// Max-height Percentage
// Example .rz-max-h-25
@include rz-utility-list-css('max-height', $height, 'max-h', 'percent');
@include rz-utility-list-breakpoints-css('max-height', $height, $rz-breakpoints-map, 'max-h', 'percent');

// Color
// Example .rz-color-primary
@include rz-color-css('color', $rz-theme-colors-map);

// Background Color
// Example .rz-background-color-primary
@include rz-color-css('background-color', $rz-theme-colors-map);

// Border base
// Example .rz-border-start
$border: border, border-start, border-end, border-left, border-right, border-top, border-bottom;
@each $value in $border {
  .rz-#{$value} {
    border-width: 0;
    @if $value == 'border-start' {
      border-inline-start-width: var(--rz-border-width);
    }
    @else if $value == 'border-end' {
      border-inline-end-width: var(--rz-border-width);
    }
    @else {
      #{$value}-width: var(--rz-border-width);
    }
    border-style: solid;
    border-color: var(--rz-base);
  }

  .rz-#{$value}-0 {
    @if $value == 'border-start' {
      border-inline-start-width: 0 !important;
    }
    @else if $value == 'border-end' {
      border-inline-end-width: 0 !important;
    }
    @else {
      #{$value}-width: 0 !important;
    }
  }
}

// Border with color
// Example .rz-border-primary
@each $color, $value in $rz-theme-colors-map {
  .rz-border-#{$color} {
    border-width: var(--rz-border-width);
    border-style: solid;
    border-color: var(--rz-#{$color}) !important;
  }
}

// Border Color
// Example .rz-border-color-primary. Use in combination with .rz-border base classes.
@include rz-color-css('border-color', $rz-theme-colors-map);

// Ripple
.rz-ripple {
  @include rz-ripple($pseudo: true);
}