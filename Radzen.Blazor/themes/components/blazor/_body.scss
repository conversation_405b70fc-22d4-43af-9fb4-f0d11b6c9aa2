$layout-body-margin: 0 !default;
$layout-body-padding: 1rem !default;
$layout-body-border-radius: 0 !default;
$layout-body-background-color: transparent !default;

.rz-body {
  margin: var(--rz-layout-body-margin);
  padding: var(--rz-layout-body-padding);
  border-radius: var(--rz-layout-body-border-radius);
  background-color: var(--rz-layout-body-background-color);

  &:only-child {
    background-color: transparent;
  }
  
  .rz-layout & {
    transform: translateZ(0);
  }
}
