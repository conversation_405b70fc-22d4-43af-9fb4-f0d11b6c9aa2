﻿@using <PERSON><PERSON><PERSON>
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.JSInterop
@inherits FormComponentWithAutoComplete<string>
@if (Visible)
{
    <input @ref="@Element" disabled="@Disabled" readonly="@ReadOnly" name="@Name" style="@Style" @attributes="Attributes" class="@GetCssClass()" tabindex="@(Disabled ? "-1" : $"{TabIndex}")"
           placeholder="@CurrentPlaceholder" maxlength="@MaxLength" autocomplete="@AutoCompleteAttribute" aria-autocomplete="@AriaAutoCompleteAttribute" value="@Value" @onchange="@OnChange" id="@GetId()"
           oninput="Radzen.mask('@GetId()', '@Mask', '@Pattern', '@CharacterPattern')"/>
}
