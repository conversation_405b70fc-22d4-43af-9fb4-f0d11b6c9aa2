﻿@using <PERSON><PERSON><PERSON>
@using Microsoft.AspNetCore.Components.Forms
@inherits FormComponentWithAutoComplete<string>
@implements IRadzenFormComponent
@if (Visible)
{
    <input @ref="@Element" id="@GetId()" name="@Name" disabled="@Disabled" readonly="@ReadOnly" style="@Style" type="password" @attributes="Attributes" class="@GetCssClass()"
           placeholder="@CurrentPlaceholder" autocomplete="@AutoCompleteAttribute" value="@Value" @onchange="@OnChange" tabindex="@(Disabled ? "-1" : $"{TabIndex}")" />
}
