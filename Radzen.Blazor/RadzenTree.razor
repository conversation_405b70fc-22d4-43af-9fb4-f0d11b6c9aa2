@inherits RadzenComponent
@using Microsoft.AspNetCore.Components.Rendering;
@using System.Collections
@using System.Linq

@if (Visible)
{
    <div @ref="@Element" @attributes="Attributes" class="@GetCssClass()" style="@Style" id="@GetId()"
        tabindex="0" @onkeydown="@OnKeyPress" @onkeydown:preventDefault=preventKeyPress @onkeydown:stopPropagation>
    <ul class="rz-tree-container">
        <CascadingValue Value=this>
            @ChildContent
        </CascadingValue>
        @if (Data != null && Levels.Any())
            {
                <CascadingValue Value=this>
                    @RenderChildren(Data, 0)
                </CascadingValue>
            }
        </ul>
    </div>
}