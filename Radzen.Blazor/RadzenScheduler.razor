@using <PERSON><PERSON><PERSON>
@using System.Globalization;
@using Microsoft.JSInterop
@inherits RadzenComponent

@typeparam TItem
@implements IScheduler

@if (Visible)
{
<CascadingValue Value="@this">
    @ChildContent
</CascadingValue>
<div @ref=Element  style=@Style @attributes=@Attributes class=@GetCssClass() id=@GetId()>
        @if (ShowHeader)
        {
            <div class="rz-scheduler-nav">
                <div class="rz-scheduler-nav-prev-next">
                    <button type="button" tabindex="0" class="rz-button rz-prev" @onclick=@OnPrev title="@PrevText"><RadzenIcon Icon="chevron_left" /></button>
                    <button type="button" tabindex="0" class="rz-button rz-next" @onclick=@OnNext title="@NextText"><RadzenIcon Icon="chevron_right" /></button>
                    <button type="button" tabindex="0" class="rz-button rz-today" @onclick=@OnToday title="@TodayText">@TodayText</button>
                </div>
                <div class="rz-scheduler-nav-title">@SelectedView?.Title</div>
                <div class="rz-scheduler-nav-views">
                    @if (NavigationTemplate != null)
                    {
                        @NavigationTemplate
                    }
                    else
                    {
                        @foreach (var view in Views)
                        {
                            <RadzenButton Click=@(args => OnChangeView(view)) Icon=@view.Icon Text=@view.Text class="@($"{(IsSelected(view) ? " rz-state-active" : "")}")" />
                        }
                    }
                </div>
            </div>
        }
        @SelectedView?.Render()
</div>
}
