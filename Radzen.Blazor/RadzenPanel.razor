﻿@using Radzen.Blazor.Rendering
@inherits RadzenComponentWithChildren
@if (Visible)
{
    <div @ref="@Element" style="@Style" @attributes="Attributes" class="@GetCssClass()" id="@GetId()">
        <div class="rz-panel-titlebar">
            @if (!string.IsNullOrEmpty(Text) || !string.IsNullOrEmpty(Icon))
            {
                <div>
                    @if (!string.IsNullOrEmpty(Icon))
                    {
                        <i class="notranslate rzi" style="@(!string.IsNullOrEmpty(IconColor) ? $"color:{IconColor}" : null)">@Icon</i>
                    }
                    @if (!string.IsNullOrEmpty(Text))
                    {
                        <span>@Text</span>
                    }
                </div>
            }
            @HeaderTemplate
            @if (AllowCollapse)
            {
                <a id="@(GetId() + "expc")" @onclick=@Toggle class="rz-panel-titlebar-icon rz-panel-titlebar-toggler"
                   @onclick:preventDefault="true" role="button" aria-controls="rz-panel-0-content"
                   aria-expanded="@(collapsed ? "false" : "true")" aria-label="@(collapsed ? ExpandAriaLabel : CollapseAriaLabel)" title="@(collapsed ? ExpandTitle : CollapseTitle)"
                   tabindex="0" @onkeypress="@(args => OnKeyPress(args, Toggle(new MouseEventArgs())))" @onkeypress:preventDefault=preventKeyPress @onkeypress:stopPropagation>
                    <span class="notranslate rzi @(collapsed ? "rzi-plus" : "rzi-minus")"></span>
                </a>
            }
        </div>
        <Expander Expanded=@(!collapsed) CssClass="rz-panel-content-wrapper">
            <div class="rz-panel-content">
                @ChildContent
            </div>
            @if (FooterTemplate != null)
            {
                <div class="rz-panel-footer">
                    @FooterTemplate
                </div>
            }
        </Expander>
        @if (SummaryTemplate != null)
        {
            <Expander Expanded=@collapsed>
                <div class="rz-panel-content rz-panel-content-summary">
                    @SummaryTemplate
                </div>
            </Expander>
        }
    </div>
}