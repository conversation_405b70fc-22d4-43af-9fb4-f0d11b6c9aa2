@using <PERSON><PERSON><PERSON>
@using <PERSON><PERSON>zen.Blazor
@using Radzen.Blazor.Rendering
@typeparam TItem
@inherits Radzen.Blazor.CartesianSeries<TItem>
@implements IChartStackedBarSeries

<CascadingValue Value="@this">
  @ChildContent
</CascadingValue>

@code {
   public override RenderFragment Render(ScaleBase categoryScale, ScaleBase valueScale)
   {
        var value = ComposeValue(categoryScale);
        var category = ComposeCategory(valueScale);
        var ticks = Chart.CategoryScale.Ticks(Chart.ValueAxis.TickDistance);
        var style = $"clip-path: url(#{Chart.ClipPath}); -webkit-clip-path: url(#{Chart.ClipPath});";

        var barSeries = StackedBarSeries;
        var barIndex = BarIndex;
        var height = BarHeight;

        var className = $"rz-bar-series rz-series-{Chart.Series.IndexOf(this)}";

        return
        @<g class="@className">
            @foreach (var data in Items)
            {
                var y = GetBarTop(data, category);
                var x = GetBarRight(data, barIndex, category, barSeries);
                var itemValue = Value(data);
                var radius = Chart.BarOptions.Radius;
                var x0 = GetBarLeft(data, barIndex, category, barSeries);

                if (itemValue < 0)
                {
                    (x, x0) = (x0, x);
                }

                if (radius > 0)
                {
                    var items = barSeries.SelectMany(series => series.ItemsForCategory(category(data))).OfType<TItem>()
                        .Where(item =>
                        {
                            var value = Value(item);
                            return itemValue >= 0 ? value >= 0 : value < 0;
                        }).ToList();

                    var index = items.IndexOf(data);
                    var width = Math.Abs(x0 - x);

                    var nextValue = (index < items.Count - 1) ? Value(items[index + 1]) : 0;

                    if (radius > height / 2 || (index < items.Count - 1 && nextValue != 0) || radius > width)
                    {
                        radius = 0;
                    }
                }

                var r = radius.ToInvariantString();

                var path = $"M {x0.ToInvariantString()} {y.ToInvariantString()} L {(x-radius).ToInvariantString()} {y.ToInvariantString()} A {r} {r} 0 0 1 {x.ToInvariantString()} {(y+radius).ToInvariantString()} L {x.ToInvariantString()} {(y+height-radius).ToInvariantString()} A {r} {r} 0 0 1 {(x-radius).ToInvariantString()} {(y + height).ToInvariantString()} L {x0.ToInvariantString()} {(y+height).ToInvariantString()} Z";

                if (x < x0)
                {
                    path = $"M {x0.ToInvariantString()} {y.ToInvariantString()} L {(x+radius).ToInvariantString()} {y.ToInvariantString()} A {r} {r} 0 0 0 {x.ToInvariantString()} {(y+radius).ToInvariantString()} L {x.ToInvariantString()} {(y+height-radius).ToInvariantString()} A {r} {r} 0 0 0 {(x+radius).ToInvariantString()} {(y + height).ToInvariantString()} L {x0.ToInvariantString()} {(y+height).ToInvariantString()} Z";
                }

                var fill = PickColor(Items.IndexOf(data), Fills, Fill, FillRange, itemValue);
                var stroke = PickColor(Items.IndexOf(data), Strokes, Stroke, StrokeRange, itemValue);

                <Path @key="@path" D="@path" Stroke="@stroke" StrokeWidth="@StrokeWidth" Fill="@fill" LineType="@LineType" Style="@style" />
            }
        </g>;
    }
}